import prisma from "@/prisma";
import { Prisma } from "@prisma/client";
import { CombinedChat, SharedChat, OwnedChat, User as GlobalUser } from "@/types";

// Detailed type definitions matching Prisma-generated types
const chatInclude = {
  shares: {
    include: {
      sender: true
    }
  },
  user: true
} satisfies Prisma.ChatInclude;

const shareInclude = {
  chat: {
    include: {
      user: true,
      shares: {
        include: {
          sender: true
        }
      }
    }
  },
  sender: true
} satisfies Prisma.ShareInclude;

type PrismaUser = Prisma.UserGetPayload<{
  select: {
    id: true,
    name: true,
    email: true,
    role: true,
    image: true
  }
}>;

interface Session {
  user: {
    id: string;
    name?: string | null;
    email?: string | null;
    role: import('@prisma/client').UserRole;
    image?: string | null;
  };
}

interface AppSidebarProps {
  session: Session | null;
}

// Helper function to get user chats
async function getUserChatsDirect(userId: string): Promise<CombinedChat[]> {
  const ownedChats = await prisma.chat.findMany({
    where: { userId },
    include: chatInclude,
    orderBy: { createdAt: 'desc' },
  });

  const sharedChats = await prisma.share.findMany({
    where: { recipientId: userId },
    include: shareInclude,
    orderBy: { createdAt: 'desc' }
  });

  // Helper function to convert PrismaUser to GlobalUser
  const convertUser = (prismaUser: PrismaUser): GlobalUser => ({
    id: prismaUser.id,
    name: prismaUser.name || 'Unknown User',
    email: prismaUser.email || '',
    image: prismaUser.image,
    role: prismaUser.role as 'ADMIN' | 'USER'
  });

  // Explicitly map to CombinedChat type
  const mappedOwnedChats: OwnedChat[] = ownedChats.map(chat => ({
    type: 'owned',
    id: chat.id,
    title: chat.title || '',
    userId: chat.userId,
    createdAt: chat.createdAt,
    shares: chat.shares.map(share => ({
      id: share.id,
      recipientId: share.recipientId,
      createdAt: share.createdAt
    })),
    user: convertUser(chat.user),
    sharedWith: chat.shares.map(share => share.recipientId)
  }));

  const mappedSharedChats: SharedChat[] = sharedChats.map(share => ({
    type: 'shared',
    id: share.chat.id,
    title: share.chat.title || '',
    userId: share.chat.userId,
    createdAt: share.chat.createdAt,
    shares: share.chat.shares.map(s => ({
      id: s.id,
      recipientId: s.recipientId,
      createdAt: s.createdAt
    })),
    user: convertUser(share.chat.user),
    sharedBy: convertUser(share.sender),
    sharedAt: share.createdAt
  }));

  return [...mappedOwnedChats, ...mappedSharedChats];
}

export async function AppSidebar({ session }: AppSidebarProps) {
  if (!session) {
    return null;
  }

  const chats = await getUserChatsDirect(session.user.id);

  // Import the client-side sidebar component
  const { ClientSidebar } = await import('./client-sidebar');

  return <ClientSidebar session={session} initialChats={chats} />;
}