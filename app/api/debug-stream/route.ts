import { NextRequest } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    console.log('=== DEBUG STREAM ENDPOINT START ===');
    
    // Test 1: Basic response
    console.log('Test 1: Basic response');
    
    // Test 2: Parse request body
    console.log('Test 2: Parsing request body');
    const body = await req.json();
    console.log('Request body:', body);
    
    // Test 3: Try importing auth
    console.log('Test 3: Importing auth');
    const { auth } = await import("@/auth");
    console.log('Auth imported successfully');
    
    // Test 4: Try getting session
    console.log('Test 4: Getting session');
    const session = await auth();
    console.log('Session:', session?.user?.email ? 'authenticated' : 'not authenticated');
    
    // Test 5: Try importing prisma
    console.log('Test 5: Importing prisma');
    const prisma = (await import("@/prisma")).default;
    console.log('Prisma imported successfully');
    
    // Test 6: Try importing Ollama
    console.log('Test 6: Importing Ollama');
    const { Ollama } = await import('ollama');
    console.log('Ollama imported successfully');
    
    // Test 7: Try creating Ollama instance
    console.log('Test 7: Creating Ollama instance');
    const ollama = new Ollama({ host: 'http://localhost:11434' });
    console.log('Ollama instance created successfully');
    
    console.log('=== ALL TESTS PASSED ===');
    
    return Response.json({
      success: true,
      message: 'All tests passed',
      session: session?.user?.email || 'not authenticated'
    });
    
  } catch (error) {
    console.error('Debug stream error:', error);
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      cause: error instanceof Error ? error.cause : undefined
    });
    
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
