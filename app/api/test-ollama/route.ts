import { NextRequest } from 'next/server';
import { Ollama } from 'ollama';

// Create Ollama instance with explicit host configuration for M1 Mac compatibility
const ollama = new Ollama({ 
  host: 'http://localhost:11434'
});

export async function GET(req: NextRequest) {
  try {
    console.log('Testing Ollama connection...');
    
    // Test basic connection
    const models = await ollama.list();
    console.log('Ollama models:', models);
    
    // Test simple chat
    const response = await ollama.chat({
      model: 'gemma3:4b',
      messages: [
        {
          role: 'user',
          content: 'Hello, just testing!'
        }
      ],
    });
    
    console.log('Ollama response:', response);
    
    return Response.json({
      success: true,
      models: models,
      testResponse: response.message.content
    });
    
  } catch (error) {
    console.error('Ollama test error:', error);
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      cause: error instanceof Error ? error.cause : undefined
    });
    
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
