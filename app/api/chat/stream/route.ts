import { auth } from "@/auth";
import { Ollama } from 'ollama';
import prisma from "@/prisma";
import { NextRequest } from 'next/server';
import { generateChatTitleWithTimeout } from '@/lib/generate-chat-title';

// Create Ollama instance with explicit host configuration for M1 Mac compatibility
const ollama = new Ollama({
  host: 'http://localhost:11434'
});

export async function POST(req: NextRequest) {
  try {
    console.log('=== STREAM ENDPOINT START ===');
    const session = await auth();
    console.log('Session:', session?.user?.email ? 'authenticated' : 'not authenticated');

    if (!session?.user?.email) {
      console.log('Unauthorized - no session or email');
      return new Response('Unauthorized', { status: 401 });
    }

    console.log('Parsing request body...');
    const { message, chatId } = await req.json();
    console.log('Request data:', { message: message?.substring(0, 50) + '...', chatId });

    if (!message || typeof message !== 'string') {
      return new Response('Invalid message', { status: 400 });
    }

    // Get user and AI user
    console.log('Fetching users from database...');
    const [user, aiUser] = await Promise.all([
      prisma.user.findUnique({ where: { email: session.user.email } }),
      prisma.user.findUnique({ where: { email: '<EMAIL>' } })
    ]);
    console.log('Users found:', { user: !!user, aiUser: !!aiUser });

    if (!user || !aiUser) {
      console.log('User configuration error - missing user or aiUser');
      return new Response('User configuration error', { status: 500 });
    }

    // Handle chat creation or validation
    let chat;
    if (chatId) {
      // Validate existing chat
      chat = await prisma.chat.findFirst({
        where: {
          id: chatId,
          OR: [
            { userId: user.id }, // Owner
            { shares: { some: { recipientId: user.id } } } // Shared with user
          ]
        }
      });
      
      if (!chat) {
        return new Response('Chat not found or access denied', { status: 404 });
      }
    } else {
      // Generate AI title for new chat
      const aiGeneratedTitle = await generateChatTitleWithTimeout(message, 3000);

      // Create new chat with AI-generated title
      chat = await prisma.chat.create({
        data: {
          title: aiGeneratedTitle,
          userId: user.id,
        },
      });
    }

    // Save user message to database
    await prisma.message.create({
      data: {
        content: message,
        type: "USER",
        chatId: chat.id,
        senderId: user.id,
      },
    });

    // Fetch previous messages for context
    const previousMessages = await prisma.message.findMany({
      where: { chatId: chat.id },
      orderBy: { createdAt: "asc" },
      select: { content: true, type: true }
    });

    // Format messages for Ollama API
    const formattedMessages = previousMessages.map((msg) => ({
      role: msg.type === "USER" ? "user" : "assistant",
      content: msg.content
    }));

    // Create a readable stream for Server-Sent Events
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send initial metadata with full chat information
          const initialData = {
            type: 'metadata',
            chatId: chat.id,
            chatTitle: chat.title,
            isNewChat: !chatId, // Indicates if this is a newly created chat
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`)
          );

          let fullResponse = '';
          
          // Test Ollama connection first
          console.log('Testing Ollama connection...');
          try {
            const testResponse = await ollama.list();
            console.log('Ollama models available:', testResponse);
          } catch (testError) {
            console.error('Ollama connection test failed:', testError);
            throw new Error(`Ollama connection failed: ${testError instanceof Error ? testError.message : String(testError)}`);
          }

          // Stream response from Ollama
          console.log('Starting Ollama chat stream...');
          const response = await ollama.chat({
            model: 'gemma3:4b',
            messages: formattedMessages,
            stream: true,
          });
          console.log('Ollama chat stream started successfully');

          // Process the streaming response
          for await (const part of response) {
            if (part.message?.content) {
              fullResponse += part.message.content;
              
              // Send streaming chunk
              const chunkData = {
                type: 'chunk',
                content: part.message.content,
                timestamp: new Date().toISOString()
              };
              
              controller.enqueue(
                encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`)
              );
            }
          }

          // Save complete AI response to database
          await prisma.message.create({
            data: {
              content: fullResponse,
              type: "ASSISTANT",
              chatId: chat.id,
              senderId: aiUser.id,
            },
          });

          // Send completion signal
          const completionData = {
            type: 'complete',
            fullContent: fullResponse,
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(completionData)}\n\n`)
          );

          // Close the stream
          controller.close();
          
        } catch (error) {
          console.error('Streaming error:', error);
          console.error('Error details:', {
            name: error instanceof Error ? error.name : 'Unknown',
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            cause: error instanceof Error ? error.cause : undefined
          });

          // Send error to client
          const errorData = {
            type: 'error',
            message: 'An error occurred while generating the response',
            timestamp: new Date().toISOString()
          };

          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`)
          );

          controller.close();
        }
      },
    });

    // Return the stream with appropriate headers for Server-Sent Events
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('API Error:', error);
    console.error('API Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      cause: error instanceof Error ? error.cause : undefined
    });
    return new Response('Internal Server Error', { status: 500 });
  }
}
